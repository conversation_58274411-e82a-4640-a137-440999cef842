'use client'

import React from 'react'
import Link from 'next/link'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui'
import VesselIcon from '@/app/ui/vessels/vesel-icon'
import { LocationModal } from '@/app/ui/vessels/list'
import { cn } from '@/app/lib/utils'

interface VesselLocationDisplayProps {
    /** Vessel object with id, title, and other vessel properties */
    vessel?: any
    /** Vessel ID for link generation (falls back to vessel.id) */
    vesselId?: number | string
    /** Display text (falls back to vessel.title) */
    displayText?: string | boolean
    /** Whether to show the location modal (default: false) */
    showLocationModal?: boolean
    /** Whether to make tooltip mobile clickable (default: false) */
    mobileClickable?: boolean
    TextBreakpoint?: string
    SubContent?: React.ReactNode
}

export const VesselLocationDisplay: React.FC<VesselLocationDisplayProps> = ({
    vessel,
    vesselId,
    displayText = true,
    showLocationModal = false,
    TextBreakpoint = '',
    SubContent,
}) => {
    // Determine the actual vessel ID to use
    const actualVesselId = vesselId || vessel?.id

    // Determine the display text
    const actualDisplayText = displayText === true ? vessel?.title : displayText

    // Don't render if no vessel and no display text
    if (!vessel && !displayText) {
        return null
    }

    return (
        <div className="flex relative items-center gap-2.5">
            {/* Vessel Icon with Tooltip */}
            {vessel && (
                <Tooltip mobileClickable>
                    <TooltipTrigger mobileClickable>
                        <div className="size-9 flex items-center justify-center flex-shrink-0 [&_img]:!size-8 [&_svg]:!size-8">
                            <VesselIcon vessel={vessel} />
                        </div>
                    </TooltipTrigger>
                    <TooltipContent>{vessel?.title || 'Vessel'}</TooltipContent>
                </Tooltip>
            )}

            {/* Optional Link or Display Text */}
            {actualDisplayText && (
                <div className='flex flex-col'>
                    {actualVesselId != 0 ? (
                        <Link
                            href={`/vessel/info?id=${actualVesselId}`}
                            className={cn(
                                'hover:text-curious-blue-400 ',
                                TextBreakpoint
                                    ? TextBreakpoint
                                    : 'hidden laptop:block text-nowrap',
                            )}>
                            {actualDisplayText}
                        </Link>
                    ) : (
                        <span
                            className={cn(
                                'hidden ',
                                TextBreakpoint
                                    ? TextBreakpoint
                                    : 'laptop:block text-nowrap',
                            )}>
                            {actualDisplayText}
                        </span>
                    )}
                    {SubContent && (
                        SubContent
                    )}
                </div>
            )}

            {/* Optional Location Modal */}
            {showLocationModal && vessel && (
                <LocationModal
                    vessel={vessel}
                    className="absolute laptop:static -top-1.5 -right-2.5"
                    iconClassName="size-6"
                />
            )}
        </div>
    )
}

export default VesselLocationDisplay
