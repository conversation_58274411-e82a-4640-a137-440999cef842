import gql from "graphql-tag";

export const GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES = gql`
query Maintenance_Status_Activity_Report(
    $limit: Int = 1000
    $filter: ComponentMaintenanceCheckFilterFields = {}
) {
    readComponentMaintenanceChecks(
        limit: $limit
        filter: $filter
    ) {
    nodes {
      id
      workOrderNumber
      groupItemTo
      projected
      actual
      difference
      name
      startDate
      documents {
          nodes {
              id
          }
      }
      maintenanceCategoryID
      maintenanceCategory {
          id
          name
          abbreviation
      }
      completedBy {
          id
          firstName
          surname
      }
      dateCompleted
      completed
      expires
      dutyHoursAtCheck
      equipmentUsagesAtCheck
      comments
      severity
      status
      archived
      assignees {
          nodes {
              id
          }
      }
      maintenanceSchedule {
          id
          title
          description
          type
          occursEveryType
          occursEvery
          warnWithinType
          highWarnWithin
          mediumWarnWithin
          lowWarnWithin
          groupTo
          maintenanceChecks {
              nodes {
                  id
              }
          }
          engineUsage {
              nodes {
                  id
                  lastScheduleHours
                  isScheduled
                  engine {
                      id
                      title
                      currentHours
                  }
              }
          }
          inventoryID
          clientID
      }
      basicComponentID
      basicComponent {
          id
          title
      }
      assignedToID
      assignedTo{
        id
        firstName
        surname
      }
      assignedByID
      inventoryID
      inventory{
        id
        title
      }
      maintenanceScheduleID
      maintenanceCheck_Signature {
          id
      }
      clientID
      recurringID
    }
  }
}
`