import gql from "graphql-tag";

export const GET_INFRINGEMENT_NOTICES_EVENT_ENTRIES = gql`
query Infringement_Notices_Report($filter: LogBookEntryFilterFields = {}){
    readLogBookEntries(sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000){
    nodes{
      id
      startDate
      endDate
      lockedDate
      state
      vehicleID
      created
      vehicle{
        id
        title
      	registration
      }
      logBookEntrySections{
        nodes{
          id
          tripEvents(filter: {
            eventCategory: {
              eq: InfringementNotice
            }
          }){
            nodes{
              id
              eventCategory
              created
              start
              end
              lastEdited
              infringementNoticeID
              notes
              infringementNotice{
                id
                created
                time
                vesselType
                vesselName
                vesselReg
                ownerFullName
                ownerDOB
                ownerOccupation
                ownerAddress
                ownerPhone
                ownerEmail
                infringementData
                otherDescription
                geoLocation{
                  id
                  title
                  lat
                  long
                }
              }
            }
          }
        }
      }
    }
  }
}
`